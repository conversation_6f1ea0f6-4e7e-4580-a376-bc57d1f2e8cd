"""
任务调度器 - 处理任务状态自动更新
"""
import asyncio
import logging
from datetime import datetime, timezone, timedelta
from sqlalchemy.orm import Session
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from .database import SessionLocal, AsyncSessionLocal
from .models import Task
from .models import china_now, CHINA_TZ

logger = logging.getLogger(__name__)


def normalize_datetime_for_comparison(dt):
    """标准化时间对象以便比较"""
    if dt is None:
        return None

    # 如果是naive datetime，假设它是中国时区时间
    if dt.tzinfo is None:
        return dt.replace(tzinfo=CHINA_TZ)

    # 如果已经有时区信息，转换为中国时区
    return dt.astimezone(CHINA_TZ)


def update_expired_tasks():
    """更新过期任务状态为已完成"""
    db = SessionLocal()
    try:
        # 获取当前时间
        current_time = china_now()
        
        # 查找所有状态为"进行中"的任务，然后在Python中检查过期时间
        # 这样避免了数据库层面的时区比较问题
        candidate_tasks = db.query(Task).filter(
            Task.status == "进行中",
            Task.expires_at.isnot(None)
        ).all()
        
        updated_count = 0
        for task in candidate_tasks:
            # 标准化时间进行比较
            task_expires_at = normalize_datetime_for_comparison(task.expires_at)
            current_time_normalized = normalize_datetime_for_comparison(current_time)

            # 再次检查是否真的过期（防止时区问题）
            if task_expires_at and task_expires_at <= current_time_normalized:
                task.status = "已完成"
                task.updated_at = current_time
                updated_count += 1
                logger.info(f"任务 {task.id} ({task.title}) 已自动更新为已完成状态")
        
        if updated_count > 0:
            db.commit()
            logger.info(f"共更新了 {updated_count} 个过期任务的状态")
        
        return updated_count

    except Exception as e:
        logger.error(f"更新过期任务状态时发生错误: {e}")
        db.rollback()
        return 0
    finally:
        db.close()


async def update_expired_tasks_async():
    """异步更新过期任务状态为已完成"""
    async with AsyncSessionLocal() as db:
        try:
            # 获取当前时间
            current_time = china_now()

            # 查找所有状态为"进行中"的任务
            stmt = select(Task).where(
                Task.status == "进行中",
                Task.expires_at.isnot(None)
            )
            result = await db.execute(stmt)
            candidate_tasks = result.scalars().all()

            updated_count = 0
            for task in candidate_tasks:
                # 标准化时间进行比较
                task_expires_at = normalize_datetime_for_comparison(task.expires_at)
                current_time_normalized = normalize_datetime_for_comparison(current_time)

                # 再次检查是否真的过期（防止时区问题）
                if task_expires_at and task_expires_at <= current_time_normalized:
                    task.status = "已完成"
                    task.updated_at = current_time
                    updated_count += 1
                    logger.info(f"任务 {task.id} ({task.title}) 已自动更新为已完成状态")

            if updated_count > 0:
                await db.commit()
                logger.info(f"共更新了 {updated_count} 个过期任务的状态")

            return updated_count

        except Exception as e:
            logger.error(f"异步更新过期任务状态时发生错误: {e}")
            await db.rollback()
            return 0


def check_and_update_task_status(task_id: int, db: Session) -> bool:
    """检查并更新单个任务的状态（用于接口调用时检查）"""
    try:
        task = db.query(Task).filter(Task.id == task_id).first()
        if not task:
            return False
            
        # 如果任务已过期且状态为"进行中"，则更新为"已完成"
        current_time = china_now()

        # 标准化时间进行比较
        task_expires_at = normalize_datetime_for_comparison(task.expires_at)
        current_time_normalized = normalize_datetime_for_comparison(current_time)

        if (task_expires_at and
            current_time_normalized and
            task_expires_at <= current_time_normalized and
            task.status == "进行中"):

            task.status = "已完成"
            task.updated_at = current_time
            db.commit()
            logger.info(f"任务 {task.id} ({task.title}) 在接口调用时自动更新为已完成状态")
            return True
            
        return False
        
    except Exception as e:
        logger.error(f"检查任务 {task_id} 状态时发生错误: {e}")
        db.rollback()
        return False


def check_and_update_tasks_by_account(task_id: int, db: Session) -> bool:
    """通过账号接口调用时检查相关任务状态"""
    return check_and_update_task_status(task_id, db)


async def start_task_scheduler():
    """启动任务调度器"""
    logger.info("任务调度器已启动")

    while True:
        try:
            # 每5分钟检查一次过期任务
            await asyncio.sleep(300)  # 300秒 = 5分钟
            await update_expired_tasks_async()  # 使用异步版本

        except Exception as e:
            logger.error(f"任务调度器运行时发生错误: {e}")
            await asyncio.sleep(60)  # 出错时等待1分钟后重试


def init_task_scheduler():
    """初始化任务调度器（在应用启动时调用）"""
    # 立即执行一次过期任务检查
    update_expired_tasks()
    
    # 启动后台任务
    asyncio.create_task(start_task_scheduler())
    logger.info("任务调度器初始化完成")
